<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Complaint;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Inertia\Response;
use App\Exports\ComplaintExport;
use Maatwebsite\Excel\Facades\Excel;

class ComplaintController extends Controller
{
    /**
     * Display a listing of complaints with filters
     */
    public function index(Request $request): Response
    {
        $query = Complaint::with('respondedBy')
            ->orderBy('created_at', 'desc');

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }

        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        if ($request->filled('visibility')) {
            $query->where('visibility', $request->visibility);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('ticket_number', 'like', "%{$search}%")
                  ->orWhere('name', 'like', "%{$search}%")
                  ->orWhere('subject', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $complaints = $query->paginate(15)->withQueryString();

        // Statistics for dashboard
        $stats = [
            'total' => Complaint::count(),
            'pending' => Complaint::where('status', 'pending')->count(),
            'in_progress' => Complaint::where('status', 'in_progress')->count(),
            'resolved' => Complaint::where('status', 'resolved')->count(),
            'closed' => Complaint::where('status', 'closed')->count(),
            'this_month' => Complaint::whereMonth('created_at', Carbon::now()->month)->count(),
            'urgent' => Complaint::where('priority', 'urgent')->count(),
        ];

        return Inertia::render('Admin/Complaints/Index', [
            'complaints' => $complaints,
            'stats' => $stats,
            'filters' => $request->only(['status', 'category', 'priority', 'visibility', 'search', 'date_from', 'date_to']),
            'categories' => Complaint::getCategories(),
            'statuses' => Complaint::getStatuses(),
            'priorities' => Complaint::getPriorities(),
            'visibilities' => Complaint::getVisibilities(),
        ]);
    }

    /**
     * Display the specified complaint
     */
    public function show(Complaint $complaint): Response
    {
        $complaint->load('respondedBy');

        return Inertia::render('Admin/Complaints/Show', [
            'complaint' => $complaint,
            'categories' => Complaint::getCategories(),
            'statuses' => Complaint::getStatuses(),
            'priorities' => Complaint::getPriorities(),
            'visibilities' => Complaint::getVisibilities(),
            'responseTemplates' => $this->getResponseTemplates(),
        ]);
    }

    /**
     * Update complaint status and priority
     */
    public function updateStatus(Request $request, Complaint $complaint)
    {
        $validated = $request->validate([
            'status' => ['required', Rule::in(array_keys(Complaint::getStatuses()))],
            'priority' => ['required', Rule::in(array_keys(Complaint::getPriorities()))],
            'visibility' => ['required', Rule::in(array_keys(Complaint::getVisibilities()))],
            'admin_response' => 'nullable|string|max:2000',
        ]);

        $oldStatus = $complaint->status;
        
        $complaint->update([
            'status' => $validated['status'],
            'priority' => $validated['priority'],
            'visibility' => $validated['visibility'],
            'admin_response' => $validated['admin_response'],
            'responded_at' => now(),
            'responded_by' => Auth::id(),
        ]);

        // Send notification if status changed
        if ($oldStatus !== $validated['status']) {
            // Email notification will be sent automatically via ComplaintObserver
        }

        return back()->with('success', 'Status pengaduan berhasil diperbarui');
    }

    /**
     * Respond to complaint
     */
    public function respond(Request $request, Complaint $complaint)
    {
        $validated = $request->validate([
            'admin_response' => 'required|string|max:2000',
            'status' => ['nullable', Rule::in(array_keys(Complaint::getStatuses()))],
        ]);

        $updateData = [
            'admin_response' => $validated['admin_response'],
            'responded_at' => now(),
            'responded_by' => Auth::id(),
        ];

        if (isset($validated['status'])) {
            $updateData['status'] = $validated['status'];
        }

        $complaint->update($updateData);

        return back()->with('success', 'Respon berhasil dikirim');
    }

    /**
     * Bulk update complaints
     */
    public function bulkUpdate(Request $request)
    {
        $validated = $request->validate([
            'complaint_ids' => 'required|array',
            'complaint_ids.*' => 'exists:complaints,id',
            'action' => 'required|in:status,priority,delete',
            'status' => ['required_if:action,status', Rule::in(array_keys(Complaint::getStatuses()))],
            'priority' => ['required_if:action,priority', Rule::in(array_keys(Complaint::getPriorities()))],
        ]);

        $complaints = Complaint::whereIn('id', $validated['complaint_ids']);

        switch ($validated['action']) {
            case 'status':
                $complaints->update([
                    'status' => $validated['status'],
                    'responded_at' => now(),
                    'responded_by' => Auth::id(),
                ]);
                $message = 'Status pengaduan berhasil diperbarui';
                break;

            case 'priority':
                $complaints->update(['priority' => $validated['priority']]);
                $message = 'Prioritas pengaduan berhasil diperbarui';
                break;

            case 'delete':
                // Delete associated files first
                foreach ($complaints->get() as $complaint) {
                    if ($complaint->attachments) {
                        foreach ($complaint->attachments as $attachment) {
                            Storage::disk('public')->delete($attachment['path']);
                        }
                    }
                }
                $complaints->delete();
                $message = 'Pengaduan berhasil dihapus';
                break;
        }

        return back()->with('success', $message);
    }

    /**
     * Export complaints to Excel
     */
    public function exportExcel(Request $request)
    {
        $query = Complaint::with('respondedBy');

        // Apply same filters as index
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }
        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $complaints = $query->get();

        $filename = 'pengaduan-' . date('Y-m-d') . '.xlsx';

        return Excel::download(new ComplaintExport($complaints), $filename);
    }

    /**
     * Export complaints to PDF
     */
    public function exportPdf(Request $request)
    {
        $query = Complaint::with('respondedBy');

        // Apply same filters as index
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }
        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $complaints = $query->get();

        return view('admin.complaints.export-pdf', [
            'complaints' => $complaints,
            'filters' => $request->only(['status', 'category', 'priority', 'date_from', 'date_to']),
            'generated_at' => now()->format('d/m/Y H:i'),
        ]);
    }

    /**
     * Get response templates for common responses
     */
    private function getResponseTemplates(): array
    {
        return [
            'received' => 'Terima kasih atas pengaduan yang Anda sampaikan. Pengaduan Anda telah kami terima dan akan segera ditindaklanjuti oleh tim terkait.',
            'in_progress' => 'Pengaduan Anda sedang dalam proses penanganan. Tim kami sedang melakukan koordinasi untuk menyelesaikan permasalahan yang Anda sampaikan.',
            'need_info' => 'Untuk dapat menindaklanjuti pengaduan Anda dengan lebih baik, kami memerlukan informasi tambahan. Mohon dapat menghubungi kantor desa untuk koordinasi lebih lanjut.',
            'resolved' => 'Pengaduan Anda telah selesai ditangani. Terima kasih atas partisipasi Anda dalam membangun Desa Lemah Duhur yang lebih baik.',
            'not_our_authority' => 'Pengaduan yang Anda sampaikan berada di luar kewenangan pemerintah desa. Kami akan meneruskan pengaduan Anda ke instansi terkait yang berwenang.',
        ];
    }
}