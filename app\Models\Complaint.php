<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class Complaint extends Model
{
    use HasFactory;
    protected $fillable = [
        'ticket_number',
        'name',
        'email',
        'phone',
        'category',
        'subject',
        'description',
        'attachments',
        'status',
        'priority',
        'visibility',
        'admin_response',
        'responded_at',
        'responded_by'
    ];

    protected $casts = [
        'attachments' => 'array',
        'responded_at' => 'datetime'
    ];

    protected $attributes = [
        'status' => 'pending',
        'priority' => 'medium',
        'visibility' => 'private'
    ];

    // Relationships
    public function respondedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'responded_by');
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeInProgress($query)
    {
        return $query->where('status', 'in_progress');
    }

    public function scopeResolved($query)
    {
        return $query->where('status', 'resolved');
    }

    public function scopeClosed($query)
    {
        return $query->where('status', 'closed');
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    public function scopePublic($query)
    {
        return $query->where('visibility', 'public');
    }

    public function scopePrivate($query)
    {
        return $query->where('visibility', 'private');
    }

    // Accessors
    public function getCategoryLabelAttribute(): string
    {
        $categories = [
            'infrastruktur' => 'Infrastruktur',
            'pelayanan' => 'Pelayanan Publik',
            'lingkungan' => 'Lingkungan',
            'sosial' => 'Sosial Kemasyarakatan',
            'lainnya' => 'Lainnya'
        ];

        return $categories[$this->category] ?? $this->category;
    }

    public function getStatusLabelAttribute(): string
    {
        $statuses = [
            'pending' => 'Menunggu',
            'in_progress' => 'Diproses',
            'resolved' => 'Selesai',
            'closed' => 'Ditutup'
        ];

        return $statuses[$this->status] ?? $this->status;
    }

    public function getPriorityLabelAttribute(): string
    {
        $priorities = [
            'low' => 'Rendah',
            'medium' => 'Sedang',
            'high' => 'Tinggi',
            'urgent' => 'Mendesak'
        ];

        return $priorities[$this->priority] ?? $this->priority;
    }

    public function getStatusColorAttribute(): string
    {
        $colors = [
            'pending' => 'yellow',
            'in_progress' => 'blue',
            'resolved' => 'green',
            'closed' => 'gray'
        ];

        return $colors[$this->status] ?? 'gray';
    }

    public function getPriorityColorAttribute(): string
    {
        $colors = [
            'low' => 'green',
            'medium' => 'yellow',
            'high' => 'orange',
            'urgent' => 'red'
        ];

        return $colors[$this->priority] ?? 'gray';
    }

    public function getVisibilityLabelAttribute(): string
    {
        $labels = [
            'private' => 'Privat',
            'public' => 'Publik'
        ];

        return $labels[$this->visibility] ?? $this->visibility;
    }

    // Static methods
    public static function generateTicketNumber(): string
    {
        $date = Carbon::now()->format('Ymd');
        $prefix = 'LDH-' . $date . '-';
        
        // Get the last ticket number for today
        $lastTicket = static::where('ticket_number', 'like', $prefix . '%')
            ->orderBy('ticket_number', 'desc')
            ->first();

        if ($lastTicket) {
            $lastNumber = (int) substr($lastTicket->ticket_number, -3);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . str_pad($newNumber, 3, '0', STR_PAD_LEFT);
    }

    public static function getCategories(): array
    {
        return [
            'infrastruktur' => 'Infrastruktur',
            'pelayanan' => 'Pelayanan Publik',
            'lingkungan' => 'Lingkungan',
            'sosial' => 'Sosial Kemasyarakatan',
            'lainnya' => 'Lainnya'
        ];
    }

    public static function getStatuses(): array
    {
        return [
            'pending' => 'Menunggu',
            'in_progress' => 'Diproses',
            'resolved' => 'Selesai',
            'closed' => 'Ditutup'
        ];
    }

    public static function getPriorities(): array
    {
        return [
            'low' => 'Rendah',
            'medium' => 'Sedang',
            'high' => 'Tinggi',
            'urgent' => 'Mendesak'
        ];
    }

    public static function getVisibilities(): array
    {
        return [
            'private' => 'Privat',
            'public' => 'Publik'
        ];
    }
}
