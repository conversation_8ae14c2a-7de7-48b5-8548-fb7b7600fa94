import SEOHead from '@/components/Common/SEOHead';
import PublicLayout from '@/layouts/PublicLayout';
import { getImageUrl } from '@/lib/image-utils';
import { News } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { ArrowLeft, Calendar, MessageCircle, Share2, Tag } from 'lucide-react';

interface Props {
    news: News;
    relatedNews: News[];
    seoMeta?: {
        title: string;
        description: string;
        image?: string;
        url?: string;
        type?: string;
        siteName?: string;
        locale?: string;
        publishedTime?: string;
        modifiedTime?: string;
    };
    structuredData?: object;
}

export default function NewsShow({ news, relatedNews, seoMeta, structuredData }: Props) {
    // Safety check
    if (!news) {
        return (
            <PublicLayout>
                <Head>
                    <title>Berita Tidak Ditemukan - <PERSON></title>
                </Head>
                <div className="flex min-h-screen items-center justify-center bg-gray-50">
                    <div className="text-center">
                        <h1 className="mb-2 text-2xl font-bold text-gray-900">Berita Tidak Ditemukan</h1>
                        <p className="mb-4 text-gray-600"><PERSON><PERSON>, berita yang Anda cari tidak dapat ditemukan.</p>
                        <Link href="/berita" className="font-medium text-blue-600 hover:text-blue-700">
                            Kembali ke Daftar Berita
                        </Link>
                    </div>
                </div>
            </PublicLayout>
        );
    }
    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('id-ID', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
        });
    };

    const getCategoryLabel = (category: string) => {
        const categoryLabels: Record<string, string> = {
            pengumuman: 'Pengumuman',
            kegiatan: 'Kegiatan',
            pembangunan: 'Pembangunan',
            sosial: 'Sosial',
        };
        return categoryLabels[category] || category;
    };

    const getCategoryColor = (category: string) => {
        const colors: Record<string, string> = {
            pengumuman: 'bg-blue-100 text-blue-800',
            kegiatan: 'bg-green-100 text-green-800',
            pembangunan: 'bg-orange-100 text-orange-800',
            sosial: 'bg-purple-100 text-purple-800',
        };
        return colors[category] || 'bg-gray-100 text-gray-800';
    };

    const shareUrl = typeof window !== 'undefined' ? window.location.href : `https://lemah-duhur.desa.id/berita/${news.slug}`;
    const shareText = `${news.title} - Desa Lemah Duhur`;

    const handleShare = (platform: string) => {
        let url = '';

        switch (platform) {
            case 'facebook':
                url = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`;
                break;
            case 'twitter':
                url = `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}&url=${encodeURIComponent(shareUrl)}`;
                break;
            case 'whatsapp':
                url = `https://wa.me/?text=${encodeURIComponent(`${shareText} ${shareUrl}`)}`;
                break;
        }

        if (url) {
            window.open(url, '_blank', 'width=600,height=400');
        }
    };

    const handleNativeShare = async () => {
        if (navigator.share) {
            try {
                await navigator.share({
                    title: shareText,
                    text: news.excerpt,
                    url: shareUrl,
                });
            } catch (error) {
                console.log('Error sharing:', error);
            }
        }
    };

    return (
        <>
            <SEOHead
                title={seoMeta?.title}
                description={seoMeta?.description}
                image={seoMeta?.image}
                url={seoMeta?.url}
                type={seoMeta?.type}
                siteName={seoMeta?.siteName}
                locale={seoMeta?.locale}
                publishedTime={seoMeta?.publishedTime}
                modifiedTime={seoMeta?.modifiedTime}
                structuredData={structuredData}
                canonical={shareUrl}
            />
            <PublicLayout>
                <div className="min-h-screen bg-gray-50 dark:bg-gray-950">
                    <div className="mx-auto max-w-4xl px-4 py-8 sm:px-6 lg:px-8">
                        {/* Back Navigation */}
                        <div className="mb-6">
                            <Link href="/berita" className="inline-flex items-center font-medium text-blue-600 hover:text-blue-700">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Kembali ke Berita
                            </Link>
                        </div>

                        {/* Article Header */}
                        <article className="overflow-hidden rounded-lg bg-white shadow-sm dark:bg-gray-800">
                            {news.featured_image && (
                                <div className="aspect-video bg-gray-200">
                                    <img src={getImageUrl(news.featured_image, 'medium')} alt={news.title} className="h-full w-full object-cover" />
                                </div>
                            )}

                            <div className="p-6 sm:p-8">
                                {/* Article Meta */}
                                <div className="mb-6 flex flex-wrap items-center gap-4">
                                    <span
                                        className={`inline-flex items-center rounded-full px-3 py-1 text-sm font-medium ${getCategoryColor(news.category)}`}
                                    >
                                        <Tag className="mr-1 h-4 w-4" />
                                        {getCategoryLabel(news.category)}
                                    </span>
                                    <span className="flex items-center text-gray-500 dark:text-gray-400">
                                        <Calendar className="mr-2 h-4 w-4" />
                                        {formatDate(news.published_at)}
                                    </span>
                                </div>

                                {/* Article Title */}
                                <h1 className="mb-6 text-3xl leading-tight font-bold text-gray-900 sm:text-4xl dark:text-white">{news.title}</h1>

                                {/* Article Content */}
                                <div
                                    className="prose prose-lg prose-gray dark:prose-invert prose-headings:text-gray-900 dark:prose-headings:text-white prose-a:text-blue-600 prose-a:no-underline hover:prose-a:underline max-w-none"
                                    dangerouslySetInnerHTML={{ __html: news.content }}
                                />

                                {/* Share Section */}
                                <div className="mt-8 border-t border-gray-200 pt-6 dark:border-gray-700">
                                    <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Bagikan Berita Ini</h3>
                                        <div className="flex items-center gap-3">
                                            {/* Native Share (if supported) */}
                                            {typeof window !== 'undefined' && 'share' in navigator && (
                                                <button
                                                    onClick={handleNativeShare}
                                                    className="inline-flex items-center rounded-lg bg-gray-100 px-4 py-2 text-gray-700 transition-colors hover:bg-gray-200"
                                                >
                                                    <Share2 className="mr-2 h-4 w-4" />
                                                    Bagikan
                                                </button>
                                            )}

                                            {/* Social Share Buttons */}
                                            <button
                                                onClick={() => handleShare('facebook')}
                                                className="rounded-lg bg-blue-600 p-2 text-white transition-colors hover:bg-blue-700"
                                                title="Bagikan ke Facebook"
                                            >
                                                <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
                                                </svg>
                                            </button>
                                            <button
                                                onClick={() => handleShare('twitter')}
                                                className="rounded-lg bg-sky-500 p-2 text-white transition-colors hover:bg-sky-600"
                                                title="Bagikan ke Twitter"
                                            >
                                                <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z" />
                                                </svg>
                                            </button>
                                            <button
                                                onClick={() => handleShare('whatsapp')}
                                                className="rounded-lg bg-green-500 p-2 text-white transition-colors hover:bg-green-600"
                                                title="Bagikan ke WhatsApp"
                                            >
                                                <MessageCircle className="h-5 w-5" />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </article>

                        {/* Related News */}
                        {relatedNews && relatedNews.length > 0 && (
                            <div className="mt-12">
                                <h2 className="mb-6 text-2xl font-bold text-gray-900 dark:text-white">Berita Terkait</h2>
                                <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                                    {relatedNews.map((item) => (
                                        <article
                                            key={item.id}
                                            className="overflow-hidden rounded-lg bg-white shadow-sm transition-shadow hover:shadow-md dark:bg-gray-800"
                                        >
                                            {item.featured_image && (
                                                <div className="aspect-video bg-gray-200">
                                                    <img
                                                        src={getImageUrl(item.featured_image, 'original')}
                                                        alt={item.title}
                                                        className="h-full w-full object-cover"
                                                        loading="lazy"
                                                    />
                                                </div>
                                            )}
                                            <div className="p-4">
                                                <div className="mb-2 flex items-center text-sm text-gray-500 dark:text-gray-400">
                                                    <Calendar className="mr-1 h-4 w-4" />
                                                    {formatDate(item.published_at)}
                                                </div>
                                                <h3 className="mb-2 line-clamp-2 font-semibold text-gray-900 dark:text-white">
                                                    <Link href={`/berita/${item.slug}`} className="transition-colors hover:text-blue-600">
                                                        {item.title}
                                                    </Link>
                                                </h3>
                                                <p className="mb-3 line-clamp-2 text-sm text-gray-600 dark:text-gray-300">{item.excerpt}</p>
                                                <Link
                                                    href={`/berita/${item.slug}`}
                                                    className="text-sm font-medium text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                                                >
                                                    Baca Selengkapnya →
                                                </Link>
                                            </div>
                                        </article>
                                    ))}
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </PublicLayout>
        </>
    );
}
