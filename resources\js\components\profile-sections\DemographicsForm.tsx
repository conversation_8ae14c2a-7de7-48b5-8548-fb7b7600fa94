import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Plus, Trash2 } from 'lucide-react';
import React from 'react';

interface DemographicItem {
    label: string;
    value: string;
    unit?: string;
}

interface DemographicsData {
    type: 'grid' | 'list';
    items: DemographicItem[];
}

interface DemographicsFormProps {
    data: DemographicsData;
    onChange: (data: DemographicsData) => void;
    errors?: {
        type?: string;
        items?: string;
    };
}

export function DemographicsForm({ data, onChange, errors }: DemographicsFormProps) {
    const updateType = (type: 'grid' | 'list') => {
        onChange({
            ...data,
            type,
        });
    };

    const updateItem = (index: number, field: keyof DemographicItem, value: string) => {
        const newItems = [...data.items];
        newItems[index] = {
            ...newItems[index],
            [field]: value,
        };
        onChange({
            ...data,
            items: newItems,
        });
    };

    const addItem = () => {
        onChange({
            ...data,
            items: [...data.items, { label: '', value: '', unit: '' }],
        });
    };

    const removeItem = (index: number) => {
        const newItems = data.items.filter((_, i) => i !== index);
        onChange({
            ...data,
            items: newItems,
        });
    };

    const renderPreview = () => {
        if (data.type === 'grid') {
            return (
                <div className="grid grid-cols-2 gap-4">
                    {data.items.map((item, index) => (
                        <div key={index}>
                            <h4 className="font-semibold">{item.label || 'Label'}</h4>
                            <p>{item.value || 'Nilai'} {item.unit || ''}</p>
                        </div>
                    ))}
                </div>
            );
        } else {
            return (
                <ul className="list-disc list-inside space-y-1">
                    {data.items.map((item, index) => (
                        <li key={index}>
                            {item.label || 'Label'}: {item.value || 'Nilai'} {item.unit || ''}
                        </li>
                    ))}
                </ul>
            );
        }
    };

    return (
        <div className="space-y-6">
            {/* Display Type */}
            <div className="space-y-2">
                <Label>Tipe Tampilan *</Label>
                <Select value={data.type} onValueChange={updateType}>
                    <SelectTrigger>
                        <SelectValue placeholder="Pilih tipe tampilan" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="grid">Grid (2 kolom)</SelectItem>
                        <SelectItem value="list">List (bullet points)</SelectItem>
                    </SelectContent>
                </Select>
                {errors?.type && <p className="text-sm text-red-600">{errors.type}</p>}
                <p className="text-xs text-gray-500">
                    Grid cocok untuk data statistik, List cocok untuk data kategori
                </p>
            </div>

            {/* Data Items */}
            <div className="space-y-4">
                <div className="flex items-center justify-between">
                    <Label>Data Demografis</Label>
                    <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={addItem}
                        className="flex items-center gap-2"
                    >
                        <Plus className="h-4 w-4" />
                        Tambah Data
                    </Button>
                </div>

                {data.items.map((item, index) => (
                    <div key={index} className="border rounded-lg p-4 space-y-3">
                        <div className="flex items-center justify-between">
                            <h4 className="font-medium">Data #{index + 1}</h4>
                            {data.items.length > 1 && (
                                <Button
                                    type="button"
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => removeItem(index)}
                                    className="text-red-600 hover:text-red-700"
                                >
                                    <Trash2 className="h-4 w-4" />
                                </Button>
                            )}
                        </div>

                        <div className="grid grid-cols-1 gap-3 md:grid-cols-3">
                            {/* Label */}
                            <div className="space-y-1">
                                <Label htmlFor={`label-${index}`}>Label *</Label>
                                <Input
                                    id={`label-${index}`}
                                    value={item.label}
                                    onChange={(e) => updateItem(index, 'label', e.target.value)}
                                    placeholder="Contoh: Jumlah Penduduk"
                                />
                            </div>

                            {/* Value */}
                            <div className="space-y-1">
                                <Label htmlFor={`value-${index}`}>Nilai *</Label>
                                <Input
                                    id={`value-${index}`}
                                    value={item.value}
                                    onChange={(e) => updateItem(index, 'value', e.target.value)}
                                    placeholder="Contoh: 2.847"
                                />
                            </div>

                            {/* Unit */}
                            <div className="space-y-1">
                                <Label htmlFor={`unit-${index}`}>Satuan</Label>
                                <Input
                                    id={`unit-${index}`}
                                    value={item.unit || ''}
                                    onChange={(e) => updateItem(index, 'unit', e.target.value)}
                                    placeholder="Contoh: jiwa, %, ha"
                                />
                            </div>
                        </div>
                    </div>
                ))}

                {data.items.length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                        <p>Belum ada data demografis</p>
                        <Button
                            type="button"
                            variant="outline"
                            onClick={addItem}
                            className="mt-2"
                        >
                            Tambah Data Pertama
                        </Button>
                    </div>
                )}

                {errors?.items && <p className="text-sm text-red-600">{errors.items}</p>}
            </div>

            {/* Preview */}
            {data.items.length > 0 && (
                <div className="space-y-2">
                    <Label>Preview</Label>
                    <div className="border rounded-lg p-4 bg-gray-50 dark:bg-gray-800">
                        {renderPreview()}
                    </div>
                </div>
            )}
        </div>
    );
}

// Helper function to convert form data to HTML
export function demographicsDataToHtml(data: DemographicsData): string {
    if (data.type === 'grid') {
        let html = '<div class="grid grid-cols-2 gap-4">';
        data.items.forEach(item => {
            html += '<div>';
            html += `<h4>${item.label}</h4>`;
            html += `<p>${item.value}${item.unit ? ' ' + item.unit : ''}</p>`;
            html += '</div>';
        });
        html += '</div>';
        return html;
    } else {
        let html = '<ul>';
        data.items.forEach(item => {
            html += `<li>${item.label}: ${item.value}${item.unit ? ' ' + item.unit : ''}</li>`;
        });
        html += '</ul>';
        return html;
    }
}

// Helper function to parse HTML back to form data
export function htmlToDemographicsData(html: string): DemographicsData {
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;
    
    const data: DemographicsData = {
        type: 'list',
        items: [],
    };
    
    // Check if it's a grid layout
    const gridDiv = tempDiv.querySelector('.grid');
    if (gridDiv) {
        data.type = 'grid';
        const gridItems = gridDiv.querySelectorAll('div');
        gridItems.forEach(item => {
            const h4 = item.querySelector('h4');
            const p = item.querySelector('p');
            if (h4 && p) {
                const label = h4.textContent || '';
                const valueText = p.textContent || '';
                
                // Try to separate value and unit
                const parts = valueText.trim().split(' ');
                const value = parts[0];
                const unit = parts.slice(1).join(' ');
                
                data.items.push({ label, value, unit });
            }
        });
    } else {
        // It's a list layout
        const listItems = tempDiv.querySelectorAll('li');
        listItems.forEach(li => {
            const text = li.textContent || '';
            const colonIndex = text.indexOf(':');
            if (colonIndex > -1) {
                const label = text.substring(0, colonIndex).trim();
                const valueText = text.substring(colonIndex + 1).trim();
                
                // Try to separate value and unit
                const parts = valueText.split(' ');
                const value = parts[0];
                const unit = parts.slice(1).join(' ');
                
                data.items.push({ label, value, unit });
            }
        });
    }
    
    return data;
}
