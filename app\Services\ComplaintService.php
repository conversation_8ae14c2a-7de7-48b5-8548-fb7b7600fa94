<?php

namespace App\Services;

use App\Models\Complaint;
use Illuminate\Support\Facades\Log;

class ComplaintService
{
    /**
     * Valid status transitions
     */
    private const STATUS_TRANSITIONS = [
        'pending' => ['in_progress', 'closed'],
        'in_progress' => ['resolved', 'pending', 'closed'],
        'resolved' => ['closed', 'in_progress'],
        'closed' => [] // Cannot transition from closed
    ];

    /**
     * Status workflow descriptions
     */
    private const STATUS_DESCRIPTIONS = [
        'pending' => 'Pengaduan baru diterima dan menunggu ditindaklanjuti',
        'in_progress' => 'Pengaduan sedang dalam proses penanganan',
        'resolved' => 'Pengaduan telah diselesaikan',
        'closed' => 'Pengaduan ditutup'
    ];

    /**
     * Check if status transition is valid
     */
    public function isValidStatusTransition(string $currentStatus, string $newStatus): bool
    {
        if (!isset(self::STATUS_TRANSITIONS[$currentStatus])) {
            return false;
        }

        return in_array($newStatus, self::STATUS_TRANSITIONS[$currentStatus]);
    }

    /**
     * Get valid next statuses for current status
     */
    public function getValidNextStatuses(string $currentStatus): array
    {
        return self::STATUS_TRANSITIONS[$currentStatus] ?? [];
    }

    /**
     * Get status description
     */
    public function getStatusDescription(string $status): string
    {
        return self::STATUS_DESCRIPTIONS[$status] ?? $status;
    }

    /**
     * Update complaint status with validation
     */
    public function updateStatus(Complaint $complaint, string $newStatus, ?string $adminResponse = null, ?int $respondedBy = null): bool
    {
        if (!$this->isValidStatusTransition($complaint->status, $newStatus)) {
            Log::warning('Invalid status transition attempted', [
                'ticket_number' => $complaint->ticket_number,
                'current_status' => $complaint->status,
                'new_status' => $newStatus
            ]);
            return false;
        }

        $updateData = ['status' => $newStatus];

        if ($adminResponse) {
            $updateData['admin_response'] = $adminResponse;
        }

        if ($respondedBy) {
            $updateData['responded_by'] = $respondedBy;
        }

        // Set responded_at if providing response
        if ($adminResponse && !$complaint->responded_at) {
            $updateData['responded_at'] = now();
        }

        $complaint->update($updateData);

        Log::info('Complaint status updated', [
            'ticket_number' => $complaint->ticket_number,
            'previous_status' => $complaint->getOriginal('status'),
            'new_status' => $newStatus,
            'responded_by' => $respondedBy
        ]);

        return true;
    }

    /**
     * Get complaint statistics
     */
    public function getStatistics(): array
    {
        return [
            'total' => Complaint::count(),
            'pending' => Complaint::where('status', 'pending')->count(),
            'in_progress' => Complaint::where('status', 'in_progress')->count(),
            'resolved' => Complaint::where('status', 'resolved')->count(),
            'closed' => Complaint::where('status', 'closed')->count(),
            'today' => Complaint::whereDate('created_at', today())->count(),
            'this_week' => Complaint::whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count(),
            'this_month' => Complaint::whereMonth('created_at', now()->month)->count(),
        ];
    }

    /**
     * Get priority statistics
     */
    public function getPriorityStatistics(): array
    {
        return [
            'low' => Complaint::where('priority', 'low')->count(),
            'medium' => Complaint::where('priority', 'medium')->count(),
            'high' => Complaint::where('priority', 'high')->count(),
            'urgent' => Complaint::where('priority', 'urgent')->count(),
        ];
    }

    /**
     * Get category statistics
     */
    public function getCategoryStatistics(): array
    {
        $categories = Complaint::getCategories();
        $stats = [];

        foreach ($categories as $key => $label) {
            $stats[$key] = [
                'label' => $label,
                'count' => Complaint::where('category', $key)->count()
            ];
        }

        return $stats;
    }

    /**
     * Calculate average response time in hours
     */
    public function getAverageResponseTime(): ?float
    {
        $complaints = Complaint::whereNotNull('responded_at')
            ->whereNotNull('created_at')
            ->get();

        if ($complaints->isEmpty()) {
            return null;
        }

        $totalHours = 0;
        foreach ($complaints as $complaint) {
            $hours = $complaint->created_at->diffInHours($complaint->responded_at);
            $totalHours += $hours;
        }

        return round($totalHours / $complaints->count(), 2);
    }

    /**
     * Get overdue complaints (pending > 7 days)
     */
    public function getOverdueComplaints()
    {
        return Complaint::where('status', 'pending')
            ->where('created_at', '<', now()->subDays(7))
            ->orderBy('created_at', 'asc')
            ->get();
    }

    /**
     * Get recent complaints
     */
    public function getRecentComplaints(int $limit = 10)
    {
        return Complaint::with('respondedBy')
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }
}