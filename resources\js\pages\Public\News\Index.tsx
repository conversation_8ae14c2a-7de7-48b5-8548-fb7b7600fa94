import SEOHead from '@/components/Common/SEOHead';
import PublicLayout from '@/layouts/PublicLayout';
import { getImageUrl } from '@/lib/image-utils';
import { PaginatedNews } from '@/types';
import { Link, router } from '@inertiajs/react';
import { Calendar, ChevronLeft, ChevronRight, Search, Tag } from 'lucide-react';
import { useState } from 'react';

interface Props {
    news: PaginatedNews;
    categories: string[];
    filters: {
        category?: string;
        search?: string;
    };
    seoMeta?: {
        title: string;
        description: string;
        image?: string;
        url?: string;
        type?: string;
        siteName?: string;
        locale?: string;
    };
    structuredData?: object;
}

export default function NewsIndex({ news, categories, filters, seoMeta, structuredData }: Props) {
    const [searchTerm, setSearchTerm] = useState(filters.search || '');
    const [selectedCategory, setSelectedCategory] = useState(filters.category || '');

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        router.get(
            '/berita',
            {
                search: searchTerm || undefined,
                category: selectedCategory || undefined,
            },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const handleCategoryFilter = (category: string) => {
        const newCategory = category === selectedCategory ? '' : category;
        setSelectedCategory(newCategory);
        router.get(
            '/berita',
            {
                search: searchTerm || undefined,
                category: newCategory || undefined,
            },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('id-ID', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
        });
    };

    const getCategoryLabel = (category: string) => {
        const categoryLabels: Record<string, string> = {
            pengumuman: 'Pengumuman',
            kegiatan: 'Kegiatan',
            pembangunan: 'Pembangunan',
            sosial: 'Sosial',
        };
        return categoryLabels[category] || category;
    };

    const getCategoryColor = (category: string) => {
        const colors: Record<string, string> = {
            pengumuman: 'bg-blue-100 text-blue-800',
            kegiatan: 'bg-green-100 text-green-800',
            pembangunan: 'bg-orange-100 text-orange-800',
            sosial: 'bg-purple-100 text-purple-800',
        };
        return colors[category] || 'bg-gray-100 text-gray-800';
    };

    return (
        <>
            <SEOHead
                title={seoMeta?.title}
                description={seoMeta?.description}
                image={seoMeta?.image}
                url={seoMeta?.url}
                type={seoMeta?.type}
                siteName={seoMeta?.siteName}
                locale={seoMeta?.locale}
                structuredData={structuredData}
            />
            <PublicLayout>
                <div className="min-h-screen bg-gray-50 dark:bg-gray-950">
                    {/* Header Section */}
                    <div className="bg-white shadow-sm dark:bg-gray-900">
                        <div className="mx-auto max-w-7xl px-3 py-6 sm:px-4 sm:py-8 lg:px-8">
                            <div className="text-center">
                                <h1 className="mb-2 text-2xl font-bold text-gray-900 sm:text-3xl dark:text-white">Berita Desa Lemah Duhur</h1>
                                <p className="mx-auto max-w-2xl px-4 text-sm text-gray-600 sm:px-0 sm:text-base dark:text-gray-300">
                                    Informasi terbaru, pengumuman, dan kegiatan dari Desa Lemah Duhur
                                </p>
                            </div>
                        </div>
                    </div>

                    <div className="mx-auto max-w-7xl px-3 py-6 sm:px-4 sm:py-8 lg:px-8">
                        {/* Search and Filter Section */}
                        <div className="mb-6 rounded-lg bg-white p-4 shadow-sm sm:mb-8 sm:p-6 dark:bg-gray-800">
                            <form onSubmit={handleSearch} className="mb-4 flex flex-col gap-3 sm:flex-row sm:gap-4">
                                <div className="relative flex-1">
                                    <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400 sm:h-5 sm:w-5" />
                                    <input
                                        type="text"
                                        placeholder="Cari berita..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                        className="w-full touch-manipulation rounded-lg border border-gray-300 py-3 pr-4 pl-9 text-base focus:border-transparent focus:ring-2 focus:ring-blue-500 sm:py-2 sm:pl-10 sm:text-sm dark:border-gray-700 dark:bg-gray-900 dark:text-white"
                                    />
                                </div>
                                <button
                                    type="submit"
                                    className="touch-manipulation rounded-lg bg-blue-600 px-6 py-3 text-base font-medium text-white transition-colors hover:bg-blue-700 sm:py-2 sm:text-sm dark:bg-blue-900 dark:hover:bg-blue-800"
                                >
                                    Cari
                                </button>
                            </form>

                            {/* Category Filter */}
                            <div className="flex flex-wrap gap-2">
                                <button
                                    onClick={() => handleCategoryFilter('')}
                                    className={`min-h-[44px] touch-manipulation rounded-full px-3 py-2 text-sm font-medium transition-colors sm:min-h-0 sm:px-4 sm:py-2 ${
                                        !selectedCategory
                                            ? 'bg-blue-600 text-white dark:bg-blue-900'
                                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700'
                                    }`}
                                >
                                    Semua
                                </button>
                                {categories.map((category) => (
                                    <button
                                        key={category}
                                        onClick={() => handleCategoryFilter(category)}
                                        className={`min-h-[44px] touch-manipulation rounded-full px-3 py-2 text-sm font-medium transition-colors sm:min-h-0 sm:px-4 sm:py-2 ${
                                            selectedCategory === category
                                                ? 'bg-blue-600 text-white dark:bg-blue-900'
                                                : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700'
                                        }`}
                                    >
                                        {getCategoryLabel(category)}
                                    </button>
                                ))}
                            </div>
                        </div>

                        {/* News Grid */}
                        {news.data.length > 0 ? (
                            <>
                                <div className="mb-6 grid grid-cols-1 gap-4 sm:mb-8 sm:grid-cols-2 sm:gap-6 lg:grid-cols-3">
                                    {news.data.map((item) => (
                                        <article
                                            key={item.id}
                                            className="touch-manipulation overflow-hidden rounded-lg bg-white shadow-sm transition-shadow hover:shadow-md dark:bg-gray-800"
                                        >
                                            {item.featured_image && (
                                                <div className="relative aspect-video bg-gray-200">
                                                    <img
                                                        src={getImageUrl(item.featured_image, 'medium')}
                                                        alt={item.title}
                                                        className="h-full w-full object-cover"
                                                        loading="lazy"
                                                        onError={(e) => {
                                                            const target = e.target as HTMLImageElement;
                                                            target.style.display = 'none';
                                                            const parent = target.parentElement;
                                                            if (parent) {
                                                                parent.innerHTML = `
                                                                    <div class="absolute inset-0 flex flex-col items-center justify-center text-gray-400">
                                                                        <svg class="h-8 w-8 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                                        </svg>
                                                                        <div class="text-xs text-center">Gambar tidak tersedia</div>
                                                                    </div>
                                                                `;
                                                            }
                                                        }}
                                                    />
                                                </div>
                                            )}
                                            <div className="p-4 sm:p-6">
                                                <div className="mb-3 flex flex-wrap items-center gap-2 sm:gap-3">
                                                    <span
                                                        className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${getCategoryColor(item.category)} dark:bg-opacity-80`}
                                                    >
                                                        <Tag className="mr-1 h-3 w-3" />
                                                        {getCategoryLabel(item.category)}
                                                    </span>
                                                    <span className="flex items-center text-xs text-gray-500 sm:text-sm dark:text-gray-400">
                                                        <Calendar className="mr-1 h-3 w-3 sm:h-4 sm:w-4" />
                                                        {formatDate(item.published_at)}
                                                    </span>
                                                </div>
                                                <h2 className="mb-2 line-clamp-2 text-lg font-semibold text-gray-900 sm:text-xl dark:text-white">
                                                    <Link
                                                        href={`/berita/${item.slug}`}
                                                        className="touch-manipulation transition-colors hover:text-blue-600"
                                                    >
                                                        {item.title}
                                                    </Link>
                                                </h2>
                                                <p className="mb-4 line-clamp-3 text-sm text-gray-600 sm:text-base dark:text-gray-300">
                                                    {item.excerpt}
                                                </p>
                                                <Link
                                                    href={`/berita/${item.slug}`}
                                                    className="inline-flex touch-manipulation items-center py-1 text-sm font-medium text-blue-600 hover:text-blue-700 sm:text-base dark:text-blue-400 dark:hover:text-blue-300"
                                                >
                                                    Baca Selengkapnya
                                                    <ChevronRight className="ml-1 h-4 w-4" />
                                                </Link>
                                            </div>
                                        </article>
                                    ))}
                                </div>

                                {/* Pagination */}
                                {news.last_page > 1 && (
                                    <div className="flex justify-center">
                                        <nav className="flex items-center space-x-2">
                                            {news.links.map((link, index) => {
                                                if (link.label.includes('Previous')) {
                                                    return (
                                                        <Link
                                                            key={index}
                                                            href={link.url || '#'}
                                                            className={`rounded-lg p-2 ${
                                                                link.url
                                                                    ? 'text-gray-500 hover:bg-gray-100 hover:text-gray-700 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-gray-200'
                                                                    : 'cursor-not-allowed text-gray-300 dark:text-gray-700'
                                                            }`}
                                                            preserveState
                                                        >
                                                            <ChevronLeft className="h-5 w-5" />
                                                        </Link>
                                                    );
                                                }

                                                if (link.label.includes('Next')) {
                                                    return (
                                                        <Link
                                                            key={index}
                                                            href={link.url || '#'}
                                                            className={`rounded-lg p-2 ${
                                                                link.url
                                                                    ? 'text-gray-500 hover:bg-gray-100 hover:text-gray-700 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-gray-200'
                                                                    : 'cursor-not-allowed text-gray-300 dark:text-gray-700'
                                                            }`}
                                                            preserveState
                                                        >
                                                            <ChevronRight className="h-5 w-5" />
                                                        </Link>
                                                    );
                                                }

                                                return (
                                                    <Link
                                                        key={index}
                                                        href={link.url || '#'}
                                                        className={`rounded-lg px-4 py-2 ${
                                                            link.active
                                                                ? 'bg-blue-600 text-white dark:bg-blue-900'
                                                                : 'text-gray-500 hover:bg-gray-100 hover:text-gray-700 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-gray-200'
                                                        }`}
                                                        preserveState
                                                    >
                                                        {link.label}
                                                    </Link>
                                                );
                                            })}
                                        </nav>
                                    </div>
                                )}
                            </>
                        ) : (
                            <div className="py-12 text-center">
                                <div className="mb-4 text-gray-400 dark:text-gray-600">
                                    <Search className="mx-auto h-16 w-16" />
                                </div>
                                <h3 className="mb-2 text-lg font-medium text-gray-900 dark:text-white">Tidak ada berita ditemukan</h3>
                                <p className="text-gray-500 dark:text-gray-400">
                                    {filters.search || filters.category
                                        ? 'Coba ubah kata kunci pencarian atau filter kategori'
                                        : 'Belum ada berita yang dipublikasikan'}
                                </p>
                            </div>
                        )}
                    </div>
                </div>
            </PublicLayout>
        </>
    );
}
