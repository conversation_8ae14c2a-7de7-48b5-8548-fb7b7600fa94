<?php

namespace App\Jobs;

use App\Mail\ComplaintStatusUpdated;
use App\Models\Complaint;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class SendComplaintStatusUpdate implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        public Complaint $complaint,
        public string $previousStatus
    ) {}

    public function handle(): void
    {
        try {
            // Only send email if complaint has email address
            if (!$this->complaint->email) {
                Log::info('Skipping status update email - no email address', [
                    'ticket_number' => $this->complaint->ticket_number
                ]);
                return;
            }
            
            Mail::to($this->complaint->email)->send(
                new ComplaintStatusUpdated($this->complaint, $this->previousStatus)
            );
            
            Log::info('Complaint status update sent to complainant', [
                'ticket_number' => $this->complaint->ticket_number,
                'email' => $this->complaint->email,
                'previous_status' => $this->previousStatus,
                'new_status' => $this->complaint->status
            ]);
            
        } catch (\Exception $e) {
            Log::error('Failed to send complaint status update', [
                'ticket_number' => $this->complaint->ticket_number,
                'error' => $e->getMessage()
            ]);
            
            // Re-throw the exception to trigger job retry
            throw $e;
        }
    }

    public function failed(\Throwable $exception): void
    {
        Log::error('Complaint status update job failed permanently', [
            'ticket_number' => $this->complaint->ticket_number,
            'error' => $exception->getMessage()
        ]);
    }
}