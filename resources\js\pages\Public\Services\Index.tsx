import SEOHead from '@/components/Common/SEOHead';
import PublicLayout from '@/layouts/PublicLayout';
import { Link, router, usePage } from '@inertiajs/react';
import { ArrowRight, Clock, Mail, MapPin, Phone, Search } from 'lucide-react';
import { useState } from 'react';

interface Service {
    id: number;
    name: string;
    description: string;
    cost: string;
    processing_time: string;
}

interface OperationalHours {
    weekdays: string;
    saturday: string;
    sunday: string;
    break: string;
}

interface ContactInfo {
    phone: string;
    email: string;
    address: string;
}

interface Props {
    services: Service[];
    operationalHours: OperationalHours;
    contactInfo: ContactInfo;
    seoMeta?: {
        title: string;
        description: string;
        image?: string;
        url?: string;
        type?: string;
        siteName?: string;
        locale?: string;
    };
    structuredData?: object;
}

const formatCost = (cost: string | number) => {
    if (!cost || cost === 0 || cost === '0') return 'Gratis';
    const num = typeof cost === 'number' ? cost : Number(cost);
    return isNaN(num) ? String(cost) : `Rp ${num.toLocaleString('id-ID')}`;
};

export default function ServicesIndex({ services, contactInfo, seoMeta, structuredData }: Props) {
    const { search } = usePage().props as { search?: string };
    const [searchTerm, setSearchTerm] = useState(search ?? '');

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        router.get('/layanan', { search: searchTerm || undefined }, { preserveState: true, replace: true });
    };

    const filteredServices = services.filter(
        (s) => s.name.toLowerCase().includes(searchTerm.toLowerCase()) || s.description.toLowerCase().includes(searchTerm.toLowerCase()),
    );
    return (
        <>
            <SEOHead
                title={seoMeta?.title}
                description={seoMeta?.description}
                image={seoMeta?.image}
                url={seoMeta?.url}
                type={seoMeta?.type}
                siteName={seoMeta?.siteName}
                locale={seoMeta?.locale}
                structuredData={structuredData}
            />
            <PublicLayout>
                <div className="min-h-screen bg-gray-50 dark:bg-gray-950">
                    {/* Hero Section */}
                    <div className="bg-gradient-to-r from-green-600 to-green-700 py-12 text-white sm:py-16 dark:from-green-900 dark:to-green-950 dark:text-green-100">
                        <div className="container mx-auto px-3 sm:px-4">
                            <div className="mx-auto max-w-4xl text-center">
                                <h1 className="mb-4 text-2xl font-bold sm:text-3xl md:text-4xl lg:text-5xl">Layanan Publik</h1>
                                <p className="text-lg text-green-100 sm:text-xl md:text-2xl">Desa Lemah Duhur</p>
                                <p className="mx-auto mt-4 max-w-2xl px-4 text-sm text-green-100 sm:px-0 sm:text-base lg:text-lg">
                                    Kami menyediakan berbagai layanan administrasi untuk memudahkan warga dalam mengurus dokumen dan keperluan
                                    lainnya.
                                </p>
                            </div>
                        </div>
                    </div>

                    <div className="container mx-auto px-3 py-8 sm:px-4 sm:py-12">
                        <div className="grid gap-6 sm:gap-8 lg:grid-cols-3">
                            {/* Services List */}
                            <div className="lg:col-span-2">
                                <h2 className="mb-4 text-xl font-bold text-gray-900 sm:mb-6 sm:text-2xl dark:text-white">Daftar Layanan Tersedia</h2>
                                <form onSubmit={handleSearch} className="mb-4 flex flex-col gap-3 sm:flex-row sm:gap-4">
                                    <div className="relative flex-1">
                                        <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400 sm:h-5 sm:w-5" />
                                        <input
                                            type="text"
                                            placeholder="Cari layanan…"
                                            value={searchTerm}
                                            onChange={(e) => setSearchTerm(e.target.value)}
                                            className="w-full touch-manipulation rounded-lg border border-gray-300 py-3 pr-4 pl-9 text-base focus:border-transparent focus:ring-2 focus:ring-green-500 sm:py-2 sm:pl-10 sm:text-sm dark:border-gray-700 dark:bg-gray-900 dark:text-white"
                                        />
                                    </div>
                                    <button
                                        type="submit"
                                        className="touch-manipulation rounded-lg bg-green-600 px-6 py-3 text-base font-medium text-white transition-colors hover:bg-green-700 sm:py-2 sm:text-sm"
                                    >
                                        Cari
                                    </button>
                                </form>
                                <div className="grid gap-4 sm:gap-6">
                                    {filteredServices.length ? (
                                        filteredServices.map((service) => (
                                            <div
                                                key={service.id}
                                                className="touch-manipulation rounded-lg bg-white shadow-md transition-shadow duration-300 hover:shadow-lg dark:bg-gray-800"
                                            >
                                                <div className="p-4 sm:p-6">
                                                    <div className="mb-3 flex flex-col gap-2 sm:mb-4 sm:flex-row sm:items-start sm:justify-between">
                                                        <h3 className="pr-2 text-lg font-semibold text-gray-900 sm:text-xl dark:text-white">
                                                            {service.name}
                                                        </h3>
                                                        <span className="self-start rounded-full bg-green-100 px-3 py-1 text-sm font-medium text-green-800 dark:bg-green-900 dark:text-green-200">
                                                            {formatCost(service.cost)}
                                                        </span>
                                                    </div>

                                                    <p className="mb-4 line-clamp-3 text-sm text-gray-600 sm:text-base dark:text-gray-300">
                                                        {service.description}
                                                    </p>

                                                    <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
                                                        <div className="flex items-center text-xs text-gray-500 sm:text-sm dark:text-gray-400">
                                                            <Clock className="mr-1 h-4 w-4 flex-shrink-0" />
                                                            <span>Proses: {service.processing_time}</span>
                                                        </div>

                                                        <Link
                                                            href={`/layanan/${service.id}`}
                                                            className="inline-flex touch-manipulation items-center py-2 text-sm font-medium text-green-600 hover:text-green-700 sm:py-0 sm:text-base"
                                                        >
                                                            Lihat Detail
                                                            <ArrowRight className="ml-1 h-4 w-4" />
                                                        </Link>
                                                    </div>
                                                </div>
                                            </div>
                                        ))
                                    ) : (
                                        <p className="text-center text-gray-500 dark:text-gray-400">Tidak ada layanan yang cocok.</p>
                                    )}
                                </div>
                            </div>

                            {/* Sidebar */}
                            <div className="space-y-4 sm:space-y-6">
                                {/* Operational Hours */}
                                <div className="rounded-lg bg-white p-4 shadow-md sm:p-6 dark:bg-gray-800">
                                    <h3 className="mb-3 flex items-center text-base font-semibold text-gray-900 sm:mb-4 sm:text-lg dark:text-white">
                                        <Clock className="mr-2 h-4 w-4 text-green-600 sm:h-5 sm:w-5" />
                                        Jam Operasional
                                    </h3>
                                    <div className="space-y-2 text-sm">
                                        <div className="flex justify-between">
                                            <span className="text-gray-600 dark:text-gray-300">Senin - Jumat</span>
                                            <span className="font-medium">08:00 - 15:00</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-gray-600 dark:text-gray-300">Sabtu</span>
                                            <span className="font-medium">08:00 - 12:00</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-gray-600 dark:text-gray-300">Minggu</span>
                                            <span className="font-medium text-red-600">Tutup</span>
                                        </div>
                                        <div className="mt-2 border-t border-gray-200 pt-2">
                                            <div className="flex justify-between">
                                                <span className="text-gray-600 dark:text-gray-300">Istirahat</span>
                                                <span className="font-medium">12:00 - 13:00</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {/* Contact Information */}
                                <div className="rounded-lg bg-white p-4 shadow-md sm:p-6 dark:bg-gray-800">
                                    <h3 className="mb-3 text-base font-semibold text-gray-900 sm:mb-4 sm:text-lg dark:text-white">
                                        Informasi Kontak
                                    </h3>
                                    <div className="space-y-3">
                                        <div className="flex items-start">
                                            <Phone className="mt-0.5 mr-3 h-4 w-4 flex-shrink-0 text-green-600 sm:h-5 sm:w-5" />
                                            <div className="min-w-0">
                                                <p className="text-sm text-gray-600 dark:text-gray-300">Telepon</p>
                                                <p className="font-medium break-all dark:text-white">{contactInfo.phone}</p>
                                            </div>
                                        </div>

                                        <div className="flex items-start">
                                            <Mail className="mt-0.5 mr-3 h-4 w-4 flex-shrink-0 text-green-600 sm:h-5 sm:w-5" />
                                            <div className="min-w-0">
                                                <p className="text-sm text-gray-600 dark:text-gray-300">Email</p>
                                                <p className="font-medium break-all dark:text-white">{contactInfo.email}</p>
                                            </div>
                                        </div>

                                        <div className="flex items-start">
                                            <MapPin className="mt-0.5 mr-3 h-4 w-4 flex-shrink-0 text-green-600 sm:h-5 sm:w-5" />
                                            <div className="min-w-0">
                                                <p className="text-sm text-gray-600 dark:text-gray-300">Alamat</p>
                                                <p className="font-medium break-words dark:text-white">{contactInfo.address}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {/* Important Notice */}
                                <div className="rounded-lg border border-blue-200 bg-blue-50 p-4 sm:p-6 dark:border-blue-900 dark:bg-blue-950">
                                    <h3 className="mb-2 text-base font-semibold text-blue-900 sm:text-lg dark:text-blue-200">Penting!</h3>
                                    <p className="text-sm leading-relaxed text-blue-800 dark:text-blue-300">
                                        Pastikan Anda membawa semua persyaratan yang diperlukan sebelum datang ke kantor desa untuk mempercepat proses
                                        pelayanan.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </PublicLayout>
        </>
    );
}
