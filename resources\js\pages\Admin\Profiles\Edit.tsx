import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ProfileContentForm, getSectionDisplayName, getSectionDescription } from '@/components/profile-sections/ProfileContentForm';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AdminLayout from '@/layouts/AdminLayout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, useForm } from '@inertiajs/react';
import { ArrowLeft, FileText, Save, Info } from 'lucide-react';
import { FormEventHandler } from 'react';

interface Profile {
    id: number;
    section: string;
    title: string;
    content: string;
    image: string | null;
    order: number;
}

interface Props {
    profile: Profile;
    sections: Record<string, string>;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Manajemen Profil Desa',
        href: '/admin/profiles',
    },
    {
        title: 'Edit Konten',
        href: '#',
    },
];

export default function ProfilesEdit({ profile, sections }: Props) {
    const { data, setData, post, processing, errors } = useForm({
        section: profile.section,
        title: profile.title,
        content: profile.content,
        image: null as File | null,
        order: profile.order.toString(),
        _method: 'PUT',
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();

        post(route('admin.profiles.update', profile.id), {
            forceFormData: true,
        });
    };

    return (
        <AdminLayout breadcrumbs={breadcrumbs}>
            <Head title={`Edit ${profile.title} - Admin Desa Lemah Duhur`} />

            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Header */}
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Edit Konten Profil</h1>
                        <p className="text-gray-600 dark:text-gray-300">Perbarui konten profil desa</p>
                    </div>
                    <Link href={route('admin.profiles.index')}>
                        <Button variant="outline" className="flex items-center space-x-2">
                            <ArrowLeft className="h-4 w-4" />
                            <span>Kembali</span>
                        </Button>
                    </Link>
                </div>

                {/* Current Image Preview */}
                {profile.image && (
                    <Card>
                        <CardHeader>
                            <CardTitle>Gambar Saat Ini</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="flex items-center space-x-4">
                                <img src={`/storage/${profile.image}`} alt={profile.title} className="h-20 w-20 rounded-lg object-cover" />
                                <div className="text-sm text-gray-600 dark:text-gray-300">
                                    <p>Upload gambar baru untuk mengganti gambar yang ada</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* Form */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <FileText className="h-5 w-5" />
                            <span>Informasi Konten</span>
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={submit} className="space-y-6">
                            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                                {/* Section */}
                                <div className="space-y-2">
                                    <Label htmlFor="section">Bagian *</Label>
                                    <Select value={data.section} onValueChange={(value) => setData('section', value)}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Pilih bagian profil" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {Object.entries(sections).map(([key, title]) => (
                                                <SelectItem key={key} value={key}>
                                                    {title}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.section && <p className="text-sm text-red-600">{errors.section}</p>}
                                </div>

                                {/* Order */}
                                <div className="space-y-2">
                                    <Label htmlFor="order">Urutan</Label>
                                    <Input
                                        id="order"
                                        type="number"
                                        min="0"
                                        value={data.order}
                                        onChange={(e) => setData('order', e.target.value)}
                                        placeholder="Urutan tampilan"
                                    />
                                    {errors.order && <p className="text-sm text-red-600">{errors.order}</p>}
                                </div>
                            </div>

                            {/* Title */}
                            <div className="space-y-2">
                                <Label htmlFor="title">Judul *</Label>
                                <Input
                                    id="title"
                                    value={data.title}
                                    onChange={(e) => setData('title', e.target.value)}
                                    placeholder="Masukkan judul konten"
                                    required
                                />
                                {errors.title && <p className="text-sm text-red-600">{errors.title}</p>}
                            </div>

                            {/* Content */}
                            <div className="space-y-2">
                                <Label htmlFor="content">Konten *</Label>
                                {data.section && (
                                    <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                                        <h4 className="font-semibold text-blue-800 mb-1">
                                            {getSectionDisplayName(data.section)}
                                        </h4>
                                        <p className="text-sm text-blue-700">
                                            {getSectionDescription(data.section)}
                                        </p>
                                    </div>
                                )}
                                <ProfileContentForm
                                    section={data.section}
                                    content={data.content}
                                    onChange={(value) => setData('content', value)}
                                    error={!!errors.content}
                                />
                                {errors.content && <p className="text-sm text-red-600">{errors.content}</p>}
                            </div>

                            {/* Image */}
                            <div className="space-y-2">
                                <Label htmlFor="image">Gambar Baru</Label>
                                <Input
                                    id="image"
                                    type="file"
                                    accept="image/jpeg,image/png,image/jpg,image/webp"
                                    onChange={(e) => setData('image', e.target.files?.[0] || null)}
                                />
                                {errors.image && <p className="text-sm text-red-600">{errors.image}</p>}
                                <p className="text-xs text-gray-500 dark:text-gray-400">
                                    Format yang didukung: JPEG, PNG, JPG, WebP. Maksimal 2MB. Kosongkan jika tidak ingin mengubah gambar.
                                </p>
                            </div>

                            {/* Submit Button */}
                            <div className="flex items-center justify-end space-x-4 pt-6">
                                <Link href={route('admin.profiles.index')}>
                                    <Button type="button" variant="outline">
                                        Batal
                                    </Button>
                                </Link>
                                <Button type="submit" disabled={processing} className="flex items-center space-x-2">
                                    <Save className="h-4 w-4" />
                                    <span>{processing ? 'Menyimpan...' : 'Simpan Perubahan'}</span>
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
