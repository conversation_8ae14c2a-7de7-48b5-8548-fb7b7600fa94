import { RichTextEditor } from '@/components/ui/rich-text-editor';
import { OrganizationForm, organizationDataToHtml, htmlToOrganizationData } from './OrganizationForm';
import { DemographicsForm, demographicsDataToHtml, htmlToDemographicsData } from './DemographicsForm';
import React, { useEffect, useState } from 'react';

interface ProfileContentFormProps {
    section: string;
    content: string;
    onChange: (content: string) => void;
    error?: boolean;
}

export function ProfileContentForm({ section, content, onChange, error }: ProfileContentFormProps) {
    const [isInitialized, setIsInitialized] = useState(false);

    // Initialize form data when section or content changes
    useEffect(() => {
        setIsInitialized(true);
    }, [section, content]);

    if (!isInitialized) {
        return <div>Loading...</div>;
    }

    // For organization section, use structured form
    if (section === 'organization') {
        const [orgData, setOrgData] = useState(() => {
            if (content) {
                try {
                    return htmlToOrganizationData(content);
                } catch (error) {
                    console.warn('Failed to parse organization content:', error);
                    return { name: '', position: '', period: '', description: '' };
                }
            }
            return { name: '', position: '', period: '', description: '' };
        });

        const handleOrgChange = (data: typeof orgData) => {
            setOrgData(data);
            const html = organizationDataToHtml(data);
            onChange(html);
        };

        return (
            <div className="space-y-2">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                    <h4 className="font-semibold text-blue-800 mb-2">Form Struktur Organisasi</h4>
                    <p className="text-sm text-blue-700">
                        Gunakan form di bawah untuk mengisi data anggota organisasi desa. 
                        Data akan otomatis diformat dengan tampilan yang konsisten.
                    </p>
                </div>
                <OrganizationForm
                    data={orgData}
                    onChange={handleOrgChange}
                />
            </div>
        );
    }

    // For demographics section, use structured form
    if (section === 'demographics') {
        const [demoData, setDemoData] = useState(() => {
            if (content) {
                try {
                    return htmlToDemographicsData(content);
                } catch (error) {
                    console.warn('Failed to parse demographics content:', error);
                    return { type: 'list' as const, items: [] };
                }
            }
            return { type: 'list' as const, items: [] };
        });

        const handleDemoChange = (data: typeof demoData) => {
            setDemoData(data);
            const html = demographicsDataToHtml(data);
            onChange(html);
        };

        return (
            <div className="space-y-2">
                <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                    <h4 className="font-semibold text-green-800 mb-2">Form Data Demografis</h4>
                    <p className="text-sm text-green-700">
                        Gunakan form di bawah untuk mengisi data demografis desa. 
                        Anda dapat memilih tampilan grid atau list sesuai kebutuhan.
                    </p>
                </div>
                <DemographicsForm
                    data={demoData}
                    onChange={handleDemoChange}
                />
            </div>
        );
    }

    // For other sections (history, vision_mission, geography), use rich text editor
    const getSectionInfo = (section: string) => {
        switch (section) {
            case 'history':
                return {
                    title: 'Editor Sejarah Desa',
                    description: 'Gunakan editor di bawah untuk menulis sejarah desa. Anda dapat menambahkan paragraf, heading, dan format teks lainnya.',
                    placeholder: 'Tuliskan sejarah desa di sini...',
                    color: 'purple'
                };
            case 'vision_mission':
                return {
                    title: 'Editor Visi & Misi',
                    description: 'Gunakan editor di bawah untuk menulis visi dan misi desa. Anda dapat menggunakan list untuk misi dan format teks untuk visi.',
                    placeholder: 'Tuliskan visi dan misi desa di sini...',
                    color: 'indigo'
                };
            case 'geography':
                return {
                    title: 'Editor Data Geografis',
                    description: 'Gunakan editor di bawah untuk menulis informasi geografis desa. Anda dapat menambahkan deskripsi dan list untuk data geografis.',
                    placeholder: 'Tuliskan informasi geografis desa di sini...',
                    color: 'teal'
                };
            default:
                return {
                    title: 'Editor Konten',
                    description: 'Gunakan editor di bawah untuk menulis konten profil desa.',
                    placeholder: 'Masukkan konten profil desa...',
                    color: 'gray'
                };
        }
    };

    const sectionInfo = getSectionInfo(section);

    return (
        <div className="space-y-2">
            <div className={`bg-${sectionInfo.color}-50 border border-${sectionInfo.color}-200 rounded-lg p-4 mb-4`}>
                <h4 className={`font-semibold text-${sectionInfo.color}-800 mb-2`}>{sectionInfo.title}</h4>
                <p className={`text-sm text-${sectionInfo.color}-700`}>
                    {sectionInfo.description}
                </p>
            </div>
            <RichTextEditor
                value={content}
                onChange={onChange}
                placeholder={sectionInfo.placeholder}
                error={error}
            />
            <p className="text-xs text-gray-500 dark:text-gray-400">
                Gunakan toolbar di atas untuk memformat konten. Anda dapat menambahkan judul, paragraf, daftar, dan format teks lainnya.
            </p>
        </div>
    );
}

// Helper function to get section display name
export function getSectionDisplayName(section: string): string {
    const sectionNames: Record<string, string> = {
        'history': 'Sejarah Desa',
        'vision_mission': 'Visi & Misi',
        'organization': 'Struktur Organisasi',
        'demographics': 'Data Demografis',
        'geography': 'Data Geografis',
    };
    
    return sectionNames[section] || section;
}

// Helper function to get section description
export function getSectionDescription(section: string): string {
    const descriptions: Record<string, string> = {
        'history': 'Cerita tentang asal-usul dan perkembangan desa dari masa ke masa',
        'vision_mission': 'Visi dan misi desa untuk pembangunan dan kemajuan ke depan',
        'organization': 'Struktur kepemimpinan dan organisasi pemerintahan desa',
        'demographics': 'Data statistik penduduk, usia, pendidikan, dan mata pencaharian',
        'geography': 'Informasi lokasi, batas wilayah, dan penggunaan lahan desa',
    };
    
    return descriptions[section] || 'Informasi profil desa';
}
