import { Alert, AlertDescription } from '@/components/ui/alert';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import PublicLayout from '@/layouts/PublicLayout';
import { Head, router, useForm } from '@inertiajs/react';
import { AlertCircle, Mail, MessageSquare, Phone, Upload, X } from 'lucide-react';
import React, { useState } from 'react';

interface ComplaintsProps {
    categories: Record<string, string>;
    pageTitle: string;
    pageDescription: string;
}

export default function Complaints({ categories, pageTitle, pageDescription }: ComplaintsProps) {
    const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
    const [isSubmitting, setIsSubmitting] = useState(false);

    const { data, setData, errors, reset } = useForm({
        name: '',
        email: '',
        phone: '',
        category: '',
        subject: '',
        description: '',
        attachments: [] as File[],
    });

    const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(event.target.files || []);
        // Fix: Remove 'image/jpg' as it's not a valid MIME type, only 'image/jpeg' is correct
        // Added WebP support for better compatibility
        const validTypes = ['image/jpeg', 'image/png', 'image/webp'];
        const maxSize = 5 * 1024 * 1024; // 5MB

        const validFiles: File[] = [];
        const invalidFiles: string[] = [];

        files.forEach((file) => {
            if (!validTypes.includes(file.type)) {
                invalidFiles.push(`${file.name}: Hanya foto (JPG, JPEG, PNG, WebP) yang diperbolehkan`);
            } else if (file.size > maxSize) {
                invalidFiles.push(`${file.name}: Ukuran terlalu besar (maks. 5MB)`);
            } else {
                validFiles.push(file);
            }
        });

        if (invalidFiles.length > 0) {
            alert('File berikut tidak dapat diupload:\n' + invalidFiles.join('\n'));
        }

        if (selectedFiles.length + validFiles.length > 3) {
            alert('Maksimal 3 foto bukti. Silakan hapus beberapa foto terlebih dahulu.');
            return;
        }

        const newFiles = [...selectedFiles, ...validFiles];
        setSelectedFiles(newFiles);

        // Clear the input so the same file can be selected again if needed
        event.target.value = '';
    };

    const removeFile = (index: number) => {
        const newFiles = selectedFiles.filter((_, i) => i !== index);
        setSelectedFiles(newFiles);
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        // Create form data with all fields including attachments
        const formData = new FormData();

        // Add text fields
        formData.append('name', data.name);
        formData.append('email', data.email);
        formData.append('phone', data.phone);
        formData.append('category', data.category);
        formData.append('subject', data.subject);
        formData.append('description', data.description);

        // Add file attachments
        // Fix: Use indexed array format instead of attachments[] to match Laravel validation
        selectedFiles.forEach((file, index) => {
            formData.append(`attachments[${index}]`, file);
        });

        // Debug logging
        console.log('Submitting with files:', selectedFiles.length);
        console.log('FormData entries:');
        for (const [key, value] of formData.entries()) {
            if (value instanceof File) {
                console.log(key, `File: ${value.name} (${value.size} bytes)`);
            } else {
                console.log(key, value);
            }
        }

        // Submit using router.post with FormData
        setIsSubmitting(true);
        router.post(route('complaints.store'), formData, {
            onSuccess: () => {
                reset();
                setSelectedFiles([]);
                setIsSubmitting(false);
            },
            onError: (errors) => {
                console.log('Form submission errors:', errors);
                setIsSubmitting(false);
            },
            onFinish: () => {
                setIsSubmitting(false);
            },
        });
    };

    const formatFileSize = (bytes: number): string => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    return (
        <PublicLayout>
            <Head title={pageTitle} />

            <div className="min-h-screen bg-gray-50 py-8 dark:bg-neutral-900">
                <div className="container mx-auto px-4">
                    {/* Header */}
                    <div className="mb-8 text-center">
                        <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900">
                            <MessageSquare className="h-8 w-8 text-blue-600 dark:text-blue-300" />
                        </div>
                        <h1 className="mb-4 text-3xl font-bold text-gray-900 dark:text-gray-100">Pengaduan Masyarakat</h1>
                        <p className="mx-auto max-w-2xl text-lg text-gray-600 dark:text-gray-300">{pageDescription}</p>
                        <div className="mt-6 flex justify-center gap-4">
                            <div className="rounded-lg bg-green-50 px-4 py-2 dark:bg-green-900">
                                <p className="text-sm font-medium text-green-800 dark:text-green-300">✓ Respon Cepat</p>
                            </div>
                            <div className="rounded-lg bg-blue-50 px-4 py-2 dark:bg-blue-900">
                                <p className="text-sm font-medium text-blue-800 dark:text-blue-300">✓ Transparan</p>
                            </div>
                            <div className="rounded-lg bg-purple-50 px-4 py-2 dark:bg-purple-900">
                                <p className="text-sm font-medium text-purple-800 dark:text-purple-300">✓ Terpercaya</p>
                            </div>
                        </div>
                    </div>

                    <div className="mx-auto grid max-w-4xl grid-cols-1 gap-8 lg:grid-cols-3">
                        {/* Information Card */}
                        <div className="lg:col-span-1">
                            <div className="space-y-6">
                                <Card className="dark:border-neutral-700 dark:bg-neutral-800">
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2 dark:text-blue-300">
                                            <MessageSquare className="h-5 w-5 text-blue-600 dark:text-blue-300" />
                                            Informasi Pengaduan
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="rounded-lg bg-blue-50 p-3 dark:bg-blue-900">
                                            <h4 className="mb-2 text-sm font-semibold text-blue-900 dark:text-blue-200">Waktu Respon</h4>
                                            <p className="text-sm text-blue-700 dark:text-blue-300">3-7 hari kerja</p>
                                        </div>

                                        <div>
                                            <h4 className="mb-3 text-sm font-semibold text-gray-900 dark:text-gray-100">Kategori Pengaduan</h4>
                                            <div className="space-y-2">
                                                {Object.values(categories).map((category, index) => (
                                                    <div key={index} className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300">
                                                        <div className="h-1.5 w-1.5 rounded-full bg-gray-400 dark:bg-gray-600"></div>
                                                        <span>{category}</span>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>

                                        <div className="rounded-lg bg-green-50 p-3 dark:bg-green-900">
                                            <h4 className="mb-2 text-sm font-semibold text-green-900 dark:text-green-200">Kontak Langsung</h4>
                                            <div className="space-y-2 text-sm text-green-700 dark:text-green-300">
                                                <div className="flex items-center gap-2">
                                                    <Phone className="h-4 w-4 dark:text-green-300" />
                                                    <span>(021) 8750-xxxx</span>
                                                </div>
                                                <div className="flex items-center gap-2">
                                                    <Mail className="h-4 w-4 dark:text-green-300" />
                                                    <span><EMAIL></span>
                                                </div>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>

                                {/* Requirements Card */}
                                <Card className="dark:border-neutral-700 dark:bg-neutral-800">
                                    <CardHeader>
                                        <CardTitle className="text-sm text-red-600 dark:text-red-300">Persyaratan Wajib</CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-3">
                                        <div className="rounded-lg bg-red-50 p-3 dark:bg-red-900">
                                            <h4 className="mb-2 text-sm font-semibold text-red-900 dark:text-red-200">Foto Bukti</h4>
                                            <ul className="space-y-1 text-sm text-red-700 dark:text-red-300">
                                                <li>• Minimal 1 foto, maksimal 3 foto</li>
                                                <li>• Format: JPG, JPEG, PNG</li>
                                                <li>• Ukuran maksimal: 5MB per foto</li>
                                                <li>• Foto harus jelas dan relevan</li>
                                            </ul>
                                        </div>
                                    </CardContent>
                                </Card>

                                {/* Quick Links Card */}
                                <Card className="dark:border-neutral-700 dark:bg-neutral-800">
                                    <CardHeader>
                                        <CardTitle className="text-sm dark:text-gray-100">Tautan Berguna</CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-2">
                                        <a
                                            href="/pengaduan/lacak"
                                            className="block rounded-lg bg-gray-50 p-3 text-sm text-gray-700 transition-colors hover:bg-gray-100 dark:bg-neutral-700 dark:text-gray-100 dark:hover:bg-neutral-600"
                                        >
                                            🔍 Lacak Status Pengaduan
                                        </a>
                                        <a
                                            href="/pengaduan/publik"
                                            className="block rounded-lg bg-gray-50 p-3 text-sm text-gray-700 transition-colors hover:bg-gray-100 dark:bg-neutral-700 dark:text-gray-100 dark:hover:bg-neutral-600"
                                        >
                                            👥 Lihat Pengaduan Publik
                                        </a>
                                    </CardContent>
                                </Card>
                            </div>
                        </div>

                        {/* Form Card */}
                        <div className="lg:col-span-2">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Form Pengaduan</CardTitle>
                                    <CardDescription>Isi form di bawah ini dengan lengkap dan jelas</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <form onSubmit={handleSubmit} className="space-y-6">
                                        {/* Contact Information */}
                                        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                            <div>
                                                <Label htmlFor="name">
                                                    Nama Lengkap <span className="text-red-500">*</span>
                                                </Label>
                                                <Input
                                                    id="name"
                                                    type="text"
                                                    value={data.name}
                                                    onChange={(e) => setData('name', e.target.value)}
                                                    placeholder="Masukkan nama lengkap"
                                                    className={errors.name ? 'border-red-500' : ''}
                                                />
                                                {errors.name && <p className="mt-1 text-sm text-red-500">{errors.name}</p>}
                                            </div>

                                            <div>
                                                <Label htmlFor="email">Email</Label>
                                                <Input
                                                    id="email"
                                                    type="email"
                                                    value={data.email}
                                                    onChange={(e) => setData('email', e.target.value)}
                                                    placeholder="<EMAIL>"
                                                    className={errors.email ? 'border-red-500' : ''}
                                                />
                                                {errors.email && <p className="mt-1 text-sm text-red-500">{errors.email}</p>}
                                            </div>
                                        </div>

                                        <div>
                                            <Label htmlFor="phone">Nomor Telepon</Label>
                                            <Input
                                                id="phone"
                                                type="tel"
                                                value={data.phone}
                                                onChange={(e) => setData('phone', e.target.value)}
                                                placeholder="08xxxxxxxxxx"
                                                className={errors.phone ? 'border-red-500' : ''}
                                            />
                                            {errors.phone && <p className="mt-1 text-sm text-red-500">{errors.phone}</p>}
                                        </div>

                                        {(errors as Record<string, string>).contact && (
                                            <Alert variant="destructive">
                                                <AlertCircle className="h-4 w-4" />
                                                <AlertDescription>{(errors as Record<string, string>).contact}</AlertDescription>
                                            </Alert>
                                        )}

                                        {/* Complaint Details */}
                                        <div>
                                            <Label htmlFor="category">
                                                Kategori Pengaduan <span className="text-red-500">*</span>
                                            </Label>
                                            <Select value={data.category} onValueChange={(value) => setData('category', value)}>
                                                <SelectTrigger className={errors.category ? 'border-red-500' : ''}>
                                                    <SelectValue placeholder="Pilih kategori pengaduan" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {Object.entries(categories).map(([key, label]) => (
                                                        <SelectItem key={key} value={key}>
                                                            {label}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                            {errors.category && <p className="mt-1 text-sm text-red-500">{errors.category}</p>}
                                        </div>

                                        <div>
                                            <Label htmlFor="subject">
                                                Subjek Pengaduan <span className="text-red-500">*</span>
                                            </Label>
                                            <Input
                                                id="subject"
                                                type="text"
                                                value={data.subject}
                                                onChange={(e) => setData('subject', e.target.value)}
                                                placeholder="Ringkasan singkat pengaduan Anda"
                                                className={errors.subject ? 'border-red-500' : ''}
                                            />
                                            {errors.subject && <p className="mt-1 text-sm text-red-500">{errors.subject}</p>}
                                        </div>

                                        <div>
                                            <Label htmlFor="description">
                                                Deskripsi Pengaduan <span className="text-red-500">*</span>
                                            </Label>
                                            <p className="mt-1 text-sm text-gray-500">
                                                Jelaskan pengaduan Anda secara detail agar dapat ditindaklanjuti dengan tepat
                                            </p>
                                            <Textarea
                                                id="description"
                                                value={data.description}
                                                onChange={(e) => setData('description', e.target.value)}
                                                placeholder="Contoh: Jalan di RT 02/RW 01 mengalami kerusakan parah dengan banyak lubang. Kondisi ini sudah berlangsung selama 2 bulan dan menyulitkan akses kendaraan..."
                                                rows={6}
                                                className={`resize-none ${errors.description ? 'border-red-500' : ''}`}
                                            />
                                            <div className="mt-2 flex items-center justify-between">
                                                {errors.description && <p className="text-sm text-red-500">{errors.description}</p>}
                                                <p
                                                    className={`ml-auto text-sm ${
                                                        data.description.length < 50
                                                            ? 'text-red-500'
                                                            : data.description.length < 100
                                                              ? 'text-yellow-600'
                                                              : 'text-green-600'
                                                    }`}
                                                >
                                                    {data.description.length}/50 karakter minimum
                                                </p>
                                            </div>
                                        </div>

                                        {/* File Upload */}
                                        <div>
                                            <Label>
                                                Foto Bukti <span className="text-red-500">*</span>
                                            </Label>
                                            <p className="mt-1 text-sm text-gray-500">
                                                Upload foto sebagai bukti pendukung pengaduan Anda (wajib minimal 1 foto)
                                            </p>
                                            <div className="mt-3">
                                                <div className="rounded-lg border-2 border-dashed border-gray-300 p-6 text-center transition-colors hover:border-gray-400">
                                                    <Upload className="mx-auto h-12 w-12 text-gray-400" />
                                                    <div className="mt-4">
                                                        <label htmlFor="file-upload" className="cursor-pointer">
                                                            <span className="mt-2 block text-sm font-medium text-gray-900">
                                                                Klik untuk upload foto bukti
                                                            </span>
                                                            <span className="mt-1 block text-xs text-gray-500">
                                                                JPG, JPEG, PNG • Maks. 5MB per foto • 1-3 foto wajib
                                                            </span>
                                                        </label>
                                                        <input
                                                            id="file-upload"
                                                            name="file-upload"
                                                            type="file"
                                                            className="sr-only"
                                                            multiple
                                                            accept=".jpg,.jpeg,.png,image/*"
                                                            onChange={handleFileSelect}
                                                        />
                                                    </div>
                                                </div>

                                                {/* Selected Files */}
                                                {selectedFiles.length > 0 && (
                                                    <div className="mt-4">
                                                        <h4 className="mb-3 text-sm font-medium text-gray-900">
                                                            File Terpilih ({selectedFiles.length}/3)
                                                        </h4>
                                                        <div className="grid grid-cols-1 gap-3 sm:grid-cols-2">
                                                            {selectedFiles.map((file, index) => (
                                                                <div key={index} className="relative rounded-lg border bg-white p-3 shadow-sm">
                                                                    <div className="flex items-start gap-3">
                                                                        <div className="h-12 w-12 flex-shrink-0 overflow-hidden rounded-lg bg-gray-100 dark:bg-neutral-700">
                                                                            <img
                                                                                src={URL.createObjectURL(file)}
                                                                                alt="Preview foto bukti"
                                                                                className="h-full w-full object-cover"
                                                                            />
                                                                        </div>
                                                                        <div className="min-w-0 flex-1">
                                                                            <p className="truncate text-sm font-medium text-gray-900 dark:text-gray-100">
                                                                                {file.name}
                                                                            </p>
                                                                            <p className="text-xs text-gray-500 dark:text-gray-300">
                                                                                {formatFileSize(file.size)} • {file.type.split('/')[1].toUpperCase()}
                                                                            </p>
                                                                        </div>
                                                                        <Button
                                                                            type="button"
                                                                            variant="ghost"
                                                                            size="sm"
                                                                            onClick={() => removeFile(index)}
                                                                            className="h-8 w-8 p-0 text-gray-400 hover:text-red-500 dark:text-gray-300 dark:hover:text-red-400"
                                                                        >
                                                                            <X className="h-4 w-4" />
                                                                        </Button>
                                                                    </div>
                                                                </div>
                                                            ))}
                                                        </div>
                                                    </div>
                                                )}

                                                {(errors as Record<string, string>).attachments && (
                                                    <p className="mt-2 text-sm text-red-500">{(errors as Record<string, string>).attachments}</p>
                                                )}
                                            </div>
                                        </div>

                                        {/* Submit Button */}
                                        <div className="flex flex-col gap-4 pt-4">
                                            <div className="rounded-lg bg-yellow-50 p-4 dark:bg-yellow-900">
                                                <p className="text-sm text-yellow-800 dark:text-yellow-300">
                                                    <strong>Perhatian:</strong> Pastikan semua informasi dan foto bukti yang Anda berikan sudah benar.
                                                    Minimal 1 foto bukti wajib dilampirkan. Setelah dikirim, Anda akan mendapat nomor tiket untuk
                                                    melacak status pengaduan.
                                                </p>
                                            </div>
                                            <div className="flex justify-end">
                                                <Button
                                                    type="submit"
                                                    disabled={isSubmitting || data.description.length < 50 || selectedFiles.length === 0}
                                                    className="px-8 py-2.5"
                                                    size="lg"
                                                >
                                                    {isSubmitting ? (
                                                        <>
                                                            <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                                                            Mengirim...
                                                        </>
                                                    ) : (
                                                        'Kirim Pengaduan'
                                                    )}
                                                </Button>
                                            </div>
                                        </div>
                                    </form>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </div>
            </div>
        </PublicLayout>
    );
}
