<?php

namespace App\Http\Controllers;

use App\Models\Profile;
use Inertia\Inertia;

class ProfileController extends Controller
{
    public function index()
    {
        // Get profile data from database instead of hardcoded data
        $history = Profile::getHistory()->map(function ($profile) {
            return [
                'id' => $profile->id,
                'section' => $profile->section,
                'title' => $profile->title,
                'content' => $profile->content,
                'image' => $profile->getImageUrl(),
                'order' => $profile->order,
            ];
        });

        $visionMission = Profile::getVisionMission()->map(function ($profile) {
            return [
                'id' => $profile->id,
                'section' => $profile->section,
                'title' => $profile->title,
                'content' => $profile->content,
                'image' => $profile->getImageUrl(),
                'order' => $profile->order,
            ];
        });

        $demographics = Profile::getDemographics()->map(function ($profile) {
            return [
                'id' => $profile->id,
                'section' => $profile->section,
                'title' => $profile->title,
                'content' => $profile->content,
                'image' => $profile->getImageUrl(),
                'order' => $profile->order,
            ];
        });

        $geography = Profile::getGeography()->map(function ($profile) {
            return [
                'id' => $profile->id,
                'section' => $profile->section,
                'title' => $profile->title,
                'content' => $profile->content,
                'image' => $profile->getImageUrl(),
                'order' => $profile->order,
            ];
        });

        $organization = Profile::getOrganization()->map(function ($profile) {
            return [
                'id' => $profile->id,
                'section' => $profile->section,
                'title' => $profile->title,
                'content' => $profile->content,
                'image' => $profile->image,
                'order' => $profile->order,
            ];
        });

        return Inertia::render('Public/Profile', [
            'history' => $history,
            'visionMission' => $visionMission,
            'demographics' => $demographics,
            'geography' => $geography,
            'organization' => $organization,
            'seoMeta' => [
                'title' => 'Profil Desa Lemah Duhur - Sejarah, Visi & Misi',
                'description' => 'Kenali profil lengkap Desa Lemah Duhur: sejarah, visi, misi, demografi, geografi, dan infrastruktur desa yang berkembang di Kecamatan Caringin.',
                'keywords' => ['profil desa', 'lemah duhur', 'caringin', 'bogor', 'sejarah desa', 'visi misi'],
                'image' => asset('images/profile-og.jpg'),
                'url' => url('/profil'),
                'type' => 'website',
                'siteName' => 'Desa Lemah Duhur',
                'locale' => 'id_ID',
            ],
            'structuredData' => [
                '@context' => 'https://schema.org',
                '@type' => 'GovernmentOrganization',
                'name' => 'Desa Lemah Duhur',
                'url' => url('/profil'),
                'logo' => asset('images/logo-kabupaten-bogor.png'),
                'description' => 'Profil lengkap Desa Lemah Duhur termasuk sejarah, visi, misi, dan informasi demografis',
                'address' => [
                    '@type' => 'PostalAddress',
                    'streetAddress' => 'Jalan Kampung Nangoh',
                    'addressLocality' => 'Desa Lemah Duhur',
                    'addressRegion' => 'Jawa Barat',
                    'postalCode' => '16730',
                    'addressCountry' => 'Indonesia',
                ],
                'contactPoint' => [
                    '@type' => 'ContactPoint',
                    'telephone' => '+62-812-3456-7890',
                    'contactType' => 'customer service',
                    'availableLanguage' => ['Indonesian'],
                ],
            ],
        ]);
    }
}
