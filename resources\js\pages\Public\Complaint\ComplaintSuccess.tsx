import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import PublicLayout from '@/layouts/PublicLayout';
import { Head, Link } from '@inertiajs/react';
import { CheckCircle, Copy, Home, Search } from 'lucide-react';

interface Complaint {
    id: number;
    ticket_number: string;
    name: string;
    category: string;
    subject: string;
    status: string;
    created_at: string;
}

interface ComplaintSuccessProps {
    complaint: Complaint;
    pageTitle: string;
    pageDescription: string;
}

export default function ComplaintSuccess({ complaint, pageTitle, pageDescription }: ComplaintSuccessProps) {
    const copyTicketNumber = async () => {
        try {
            // Try modern clipboard API first
            if (navigator.clipboard && window.isSecureContext) {
                await navigator.clipboard.writeText(complaint.ticket_number);
                alert('Nomor tiket berhasil disalin!');
            } else {
                // Fallback for older browsers or non-secure contexts
                const textArea = document.createElement('textarea');
                textArea.value = complaint.ticket_number;
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                textArea.style.top = '-999999px';
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();

                try {
                    document.execCommand('copy');
                    alert('Nomor tiket berhasil disalin!');
                } catch {
                    // If all else fails, show the ticket number for manual copying
                    alert(`Silakan salin nomor tiket secara manual: ${complaint.ticket_number}`);
                }

                document.body.removeChild(textArea);
            }
        } catch {
            // Final fallback - show ticket number for manual copying
            alert(`Silakan salin nomor tiket secara manual: ${complaint.ticket_number}`);
        }
    };

    return (
        <PublicLayout>
            <Head title={pageTitle} />

            <div className="min-h-screen bg-gray-50 py-8 dark:bg-neutral-900">
                <div className="container mx-auto px-4">
                    <div className="mx-auto max-w-2xl">
                        {/* Success Message */}
                        <div className="mb-8 text-center">
                            <div className="mb-4 inline-flex h-16 w-16 items-center justify-center rounded-full bg-green-100 dark:bg-green-900">
                                <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-300" />
                            </div>
                            <h1 className="mb-2 text-3xl font-bold text-gray-900 dark:text-gray-100">Pengaduan Berhasil Dikirim!</h1>
                            <p className="text-lg text-gray-600 dark:text-gray-300">{pageDescription}</p>
                        </div>

                        {/* Ticket Information */}
                        <Card className="mb-6 dark:border-neutral-700 dark:bg-neutral-800">
                            <CardHeader>
                                <CardTitle className="dark:text-gray-100">Detail Pengaduan</CardTitle>
                                <CardDescription className="dark:text-gray-300">
                                    <div className="flex flex-col">
                                        <strong className="text-red-500">Nomor tiket ini hanya ditampilkan satu kali.</strong> Simpan nomor tiket ini
                                        untuk melacak status pengaduan Anda
                                    </div>
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="rounded-lg border border-blue-200 bg-blue-50 p-4 dark:border-blue-700 dark:bg-blue-900">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <p className="mb-1 text-sm font-medium text-blue-900 dark:text-blue-200">Nomor Tiket</p>
                                            <p className="text-2xl font-bold text-blue-600 dark:text-blue-300">{complaint.ticket_number}</p>
                                        </div>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={copyTicketNumber}
                                            className="border-blue-300 text-blue-600 hover:bg-blue-50 dark:border-blue-700 dark:text-blue-300 dark:hover:bg-blue-900"
                                        >
                                            <Copy className="mr-2 h-4 w-4" />
                                            Salin
                                        </Button>
                                    </div>
                                </div>

                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <p className="mb-1 text-sm font-medium text-gray-500 dark:text-gray-300">Nama Pengadu</p>
                                        <p className="text-gray-900 dark:text-gray-100">{complaint.name}</p>
                                    </div>
                                    <div>
                                        <p className="mb-1 text-sm font-medium text-gray-500 dark:text-gray-300">Kategori</p>
                                        <p className="text-gray-900 dark:text-gray-100">{complaint.category}</p>
                                    </div>
                                    <div>
                                        <p className="mb-1 text-sm font-medium text-gray-500 dark:text-gray-300">Status</p>
                                        <span className="inline-flex items-center rounded-full bg-yellow-100 px-2.5 py-0.5 text-xs font-medium text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300">
                                            {complaint.status}
                                        </span>
                                    </div>
                                    <div>
                                        <p className="mb-1 text-sm font-medium text-gray-500 dark:text-gray-300">Tanggal Pengaduan</p>
                                        <p className="text-gray-900 dark:text-gray-100">
                                            {new Date(complaint.created_at).toLocaleDateString('id-ID', {
                                                day: 'numeric',
                                                month: 'long',
                                                year: 'numeric',
                                            })}
                                        </p>
                                    </div>
                                </div>

                                <div>
                                    <p className="mb-1 text-sm font-medium text-gray-500 dark:text-gray-300">Subjek Pengaduan</p>
                                    <p className="text-gray-900 dark:text-gray-100">{complaint.subject}</p>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Next Steps */}
                        <Card className="mb-6 dark:border-neutral-700 dark:bg-neutral-800">
                            <CardHeader>
                                <CardTitle className="dark:text-gray-100">Langkah Selanjutnya</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    <div className="flex items-start gap-3">
                                        <div className="flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900">
                                            <span className="text-sm font-semibold text-blue-600 dark:text-blue-300">1</span>
                                        </div>
                                        <div>
                                            <p className="font-medium text-gray-900 dark:text-gray-100">Pengaduan Diterima</p>
                                            <p className="text-sm text-gray-600 dark:text-gray-300">
                                                Pengaduan Anda telah diterima dan akan diproses oleh petugas yang berwenang.
                                            </p>
                                        </div>
                                    </div>

                                    <div className="flex items-start gap-3">
                                        <div className="flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-gray-100 dark:bg-neutral-700">
                                            <span className="text-sm font-semibold text-gray-600 dark:text-gray-300">2</span>
                                        </div>
                                        <div>
                                            <p className="font-medium text-gray-900 dark:text-gray-100">Proses Verifikasi</p>
                                            <p className="text-sm text-gray-600 dark:text-gray-300">
                                                Tim akan melakukan verifikasi dan analisis terhadap pengaduan Anda.
                                            </p>
                                        </div>
                                    </div>

                                    <div className="flex items-start gap-3">
                                        <div className="flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-gray-100 dark:bg-neutral-700">
                                            <span className="text-sm font-semibold text-gray-600 dark:text-gray-300">3</span>
                                        </div>
                                        <div>
                                            <p className="font-medium text-gray-900 dark:text-gray-100">Tindak Lanjut</p>
                                            <p className="text-sm text-gray-600 dark:text-gray-300">
                                                Anda akan mendapat respon dan tindak lanjut dalam 3-7 hari kerja.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Action Buttons */}
                        <div className="flex flex-col justify-center gap-4 sm:flex-row">
                            <Button asChild variant="outline" className="dark:border-blue-700 dark:text-blue-300">
                                <Link href={route('complaints.tracking')}>
                                    <Search className="mr-2 h-4 w-4" />
                                    Lacak Pengaduan
                                </Link>
                            </Button>

                            <Button asChild className="dark:bg-blue-700 dark:text-white">
                                <Link href={route('home')}>
                                    <Home className="mr-2 h-4 w-4" />
                                    Kembali ke Beranda
                                </Link>
                            </Button>
                        </div>

                        {/* Contact Information */}
                        <div className="mt-8 text-center">
                            <p className="mb-2 text-sm text-gray-600 dark:text-gray-300">Butuh bantuan? Hubungi kami:</p>
                            <div className="flex justify-center gap-4 text-sm">
                                <span className="text-gray-600 dark:text-gray-300">📞 (021) 8750-xxxx</span>
                                <span className="text-gray-600 dark:text-gray-300">✉️ <EMAIL></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </PublicLayout>
    );
}
